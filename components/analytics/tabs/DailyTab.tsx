import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Analytics, DailyStat } from '@/types/app';

const { width } = Dimensions.get('window');

interface DailyTabProps {
  analytics: Analytics | null;
  formatTime: (seconds: number) => string;
  loading: boolean;
  error: string | null;
}

export function DailyTab({ analytics, formatTime, loading, error }: DailyTabProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month'>('week');

  const getDailyStatsForPeriod = (): DailyStat[] => {
    if (!analytics?.dailyStats) return [];
    
    const now = new Date();
    const daysToShow = selectedPeriod === 'week' ? 7 : 30;
    const cutoffDate = new Date();
    cutoffDate.setDate(now.getDate() - daysToShow);
    
    return analytics.dailyStats
      .filter(stat => new Date(stat.date) >= cutoffDate)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  };

  const renderPeriodSelector = () => {
    return (
      <View style={styles.periodSelector}>
        <TouchableOpacity
          style={[
            styles.periodButton,
            selectedPeriod === 'week' && styles.periodButtonActive,
          ]}
          onPress={() => setSelectedPeriod('week')}
        >
          <Text
            style={[
              styles.periodText,
              selectedPeriod === 'week' && styles.periodTextActive,
            ]}
          >
            Last 7 Days
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.periodButton,
            selectedPeriod === 'month' && styles.periodButtonActive,
          ]}
          onPress={() => setSelectedPeriod('month')}
        >
          <Text
            style={[
              styles.periodText,
              selectedPeriod === 'month' && styles.periodTextActive,
            ]}
          >
            Last 30 Days
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderBarChart = () => {
    const dailyStats = getDailyStatsForPeriod();
    if (dailyStats.length === 0) return null;

    const maxDuration = Math.max(...dailyStats.map(stat => stat.totalDuration));
    const chartHeight = 120;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Daily Study Time</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.barChart}>
            {dailyStats.reverse().map((stat, index) => {
              const height = maxDuration > 0 ? (stat.totalDuration / maxDuration) * chartHeight : 0;
              const date = new Date(stat.date);
              const isToday = date.toDateString() === new Date().toDateString();
              
              return (
                <View key={stat.date} style={styles.barContainer}>
                  <View style={[styles.barWrapper, { height: chartHeight }]}>
                    <LinearGradient
                      colors={stat.targetAchieved ? ['#10B981', '#059669'] : ['#6366F1', '#8B5CF6']}
                      style={[
                        styles.bar,
                        { 
                          height: Math.max(height, 4),
                          opacity: isToday ? 1 : 0.8
                        }
                      ]}
                    />
                  </View>
                  <Text style={[styles.barLabel, isToday && styles.barLabelToday]}>
                    {date.toLocaleDateString('en-US', { 
                      month: 'short', 
                      day: 'numeric' 
                    })}
                  </Text>
                  <Text style={styles.barValue}>
                    {formatTime(stat.totalDuration)}
                  </Text>
                  {stat.targetAchieved && (
                    <MaterialIcons name="check-circle" size={12} color="#10B981" />
                  )}
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderDailyList = () => {
    const dailyStats = getDailyStatsForPeriod();
    if (dailyStats.length === 0) return null;

    return (
      <View style={styles.listContainer}>
        <Text style={styles.listTitle}>Daily Breakdown</Text>
        {dailyStats.map((stat) => {
          const date = new Date(stat.date);
          const isToday = date.toDateString() === new Date().toDateString();
          const isYesterday = date.toDateString() === new Date(Date.now() - 86400000).toDateString();
          
          let dateLabel = date.toLocaleDateString('en-US', { 
            weekday: 'long', 
            month: 'short', 
            day: 'numeric' 
          });
          
          if (isToday) dateLabel = 'Today';
          else if (isYesterday) dateLabel = 'Yesterday';

          return (
            <View key={stat.date} style={styles.dailyItem}>
              <View style={styles.dailyHeader}>
                <View style={styles.dailyDateContainer}>
                  <Text style={[styles.dailyDate, isToday && styles.dailyDateToday]}>
                    {dateLabel}
                  </Text>
                  {stat.targetAchieved && (
                    <MaterialIcons name="check-circle" size={16} color="#10B981" />
                  )}
                </View>
                <Text style={styles.dailyDuration}>
                  {formatTime(stat.totalDuration)}
                </Text>
              </View>
              
              {stat.sessionCount > 0 && (
                <View style={styles.dailyDetails}>
                  <View style={styles.dailyDetailItem}>
                    <MaterialIcons name="play-circle-outline" size={16} color="#64748B" />
                    <Text style={styles.dailyDetailText}>
                      {stat.sessionCount} session{stat.sessionCount !== 1 ? 's' : ''}
                    </Text>
                  </View>
                  
                  {stat.completedPomodoros > 0 && (
                    <View style={styles.dailyDetailItem}>
                      <MaterialIcons name="timer" size={16} color="#64748B" />
                      <Text style={styles.dailyDetailText}>
                        {stat.completedPomodoros} pomodoro{stat.completedPomodoros !== 1 ? 's' : ''}
                      </Text>
                    </View>
                  )}
                  
                  {stat.averageProductivityRating > 0 && (
                    <View style={styles.dailyDetailItem}>
                      <MaterialIcons name="star" size={16} color="#F59E0B" />
                      <Text style={styles.dailyDetailText}>
                        {stat.averageProductivityRating.toFixed(1)} rating
                      </Text>
                    </View>
                  )}
                </View>
              )}
              
              {/* Subject breakdown */}
              {Object.keys(stat.subjectDurations).length > 0 && (
                <View style={styles.subjectBreakdown}>
                  {Object.entries(stat.subjectDurations)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 3)
                    .map(([subject, duration]) => (
                      <View key={subject} style={styles.subjectItem}>
                        <Text style={styles.subjectName}>{subject}</Text>
                        <Text style={styles.subjectDuration}>
                          {formatTime(duration)}
                        </Text>
                      </View>
                    ))}
                </View>
              )}
            </View>
          );
        })}
      </View>
    );
  };

  const renderSummaryStats = () => {
    const dailyStats = getDailyStatsForPeriod();
    if (dailyStats.length === 0) return null;

    const totalTime = dailyStats.reduce((sum, stat) => sum + stat.totalDuration, 0);
    const totalSessions = dailyStats.reduce((sum, stat) => sum + stat.sessionCount, 0);
    const daysWithStudy = dailyStats.filter(stat => stat.totalDuration > 0).length;
    const targetAchievedDays = dailyStats.filter(stat => stat.targetAchieved).length;

    return (
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>
          {selectedPeriod === 'week' ? 'Week' : 'Month'} Summary
        </Text>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{formatTime(totalTime)}</Text>
            <Text style={styles.summaryLabel}>Total Time</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{totalSessions}</Text>
            <Text style={styles.summaryLabel}>Sessions</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{daysWithStudy}</Text>
            <Text style={styles.summaryLabel}>Active Days</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{targetAchievedDays}</Text>
            <Text style={styles.summaryLabel}>Goals Met</Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <MaterialIcons name="today" size={48} color="#6366F1" />
        <Text style={styles.loadingText}>Loading daily analytics...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="error-outline" size={48} color="#EF4444" />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderPeriodSelector()}
      {renderSummaryStats()}
      {renderBarChart()}
      {renderDailyList()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  periodSelector: {
    flexDirection: 'row',
    margin: 20,
    backgroundColor: '#F1F5F9',
    borderRadius: 12,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
  },
  periodButtonActive: {
    backgroundColor: '#6366F1',
  },
  periodText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
  },
  periodTextActive: {
    color: '#FFFFFF',
  },
  summaryContainer: {
    margin: 20,
    marginTop: 0,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: '#64748B',
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    margin: 20,
    marginTop: 0,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  barChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 8,
    paddingBottom: 40,
  },
  barContainer: {
    alignItems: 'center',
    minWidth: 40,
  },
  barWrapper: {
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    width: 24,
    borderRadius: 12,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 10,
    color: '#64748B',
    marginBottom: 2,
    textAlign: 'center',
  },
  barLabelToday: {
    fontWeight: '600',
    color: '#1E293B',
  },
  barValue: {
    fontSize: 9,
    color: '#64748B',
    textAlign: 'center',
  },
  listContainer: {
    margin: 20,
    marginTop: 0,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  dailyItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  dailyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  dailyDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dailyDate: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1E293B',
  },
  dailyDateToday: {
    color: '#6366F1',
    fontWeight: '600',
  },
  dailyDuration: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6366F1',
  },
  dailyDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 8,
  },
  dailyDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dailyDetailText: {
    fontSize: 12,
    color: '#64748B',
  },
  subjectBreakdown: {
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    paddingTop: 8,
    gap: 4,
  },
  subjectItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  subjectName: {
    fontSize: 12,
    color: '#64748B',
  },
  subjectDuration: {
    fontSize: 12,
    fontWeight: '500',
    color: '#1E293B',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#64748B',
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    marginTop: 12,
    textAlign: 'center',
  },
});
