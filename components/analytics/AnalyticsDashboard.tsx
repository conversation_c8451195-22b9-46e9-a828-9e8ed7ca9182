import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { useAnalytics } from '@/hooks/useAnalytics';
import { AnalyticsPeriod } from '@/types/app';

// Tab Components
import { OverviewTab } from './tabs/OverviewTab';
import { DailyTab } from './tabs/DailyTab';
import { WeeklyTab } from './tabs/WeeklyTab';
import { MonthlyTab } from './tabs/MonthlyTab';
import { SubjectsTab } from './tabs/SubjectsTab';
import { TaskTypesTab } from './tabs/TaskTypesTab';

const { width } = Dimensions.get('window');

interface AnalyticsTab {
  id: string;
  title: string;
  icon: keyof typeof MaterialIcons.glyphMap;
}

const ANALYTICS_TABS: AnalyticsTab[] = [
  { id: 'overview', title: 'Overview', icon: 'dashboard' },
  { id: 'daily', title: 'Daily', icon: 'today' },
  { id: 'weekly', title: 'Weekly', icon: 'view-week' },
  { id: 'monthly', title: 'Monthly', icon: 'calendar-month' },
  { id: 'subjects', title: 'Subjects', icon: 'school' },
  { id: 'task-types', title: 'Task Types', icon: 'category' },
];

const PERIOD_OPTIONS: { value: AnalyticsPeriod; label: string }[] = [
  { value: 'week', label: 'Week' },
  { value: 'month', label: 'Month' },
  { value: 'year', label: 'Year' },
  { value: 'all', label: 'All Time' },
];

export function AnalyticsDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const {
    loading,
    error,
    selectedPeriod,
    changePeriod,
    refreshAnalytics,
    quickStats,
    analytics,
    timeSeriesData,
    subjectChartData,
    taskTypeChartData,
    performanceMetrics,
    todayProgress,
    weekProgress,
    formatTime,
    getStreakMessage,
  } = useAnalytics();

  const renderTabContent = () => {
    const commonProps = {
      analytics,
      timeSeriesData,
      subjectChartData,
      taskTypeChartData,
      performanceMetrics,
      quickStats,
      todayProgress,
      weekProgress,
      formatTime,
      getStreakMessage,
      loading,
      error,
    };

    switch (activeTab) {
      case 'overview':
        return <OverviewTab {...commonProps} />;
      case 'daily':
        return <DailyTab {...commonProps} />;
      case 'weekly':
        return <WeeklyTab {...commonProps} />;
      case 'monthly':
        return <MonthlyTab {...commonProps} />;
      case 'subjects':
        return <SubjectsTab {...commonProps} />;
      case 'task-types':
        return <TaskTypesTab {...commonProps} />;
      default:
        return <OverviewTab {...commonProps} />;
    }
  };

  const renderPeriodSelector = () => {
    if (activeTab === 'daily' || activeTab === 'overview') {
      // These tabs have their own period controls
      return null;
    }

    return (
      <View style={styles.periodSelector}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {PERIOD_OPTIONS.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.periodButton,
                selectedPeriod === option.value && styles.periodButtonActive,
              ]}
              onPress={() => changePeriod(option.value)}
            >
              <Text
                style={[
                  styles.periodText,
                  selectedPeriod === option.value && styles.periodTextActive,
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#6366F1', '#8B5CF6']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Analytics</Text>
          <Text style={styles.headerSubtitle}>Track your study progress</Text>
        </View>
        
        {/* Tab Navigation */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.tabScrollView}
          contentContainerStyle={styles.tabContainer}
        >
          {ANALYTICS_TABS.map((tab) => (
            <TouchableOpacity
              key={tab.id}
              style={[
                styles.tab,
                activeTab === tab.id && styles.tabActive,
              ]}
              onPress={() => setActiveTab(tab.id)}
            >
              <MaterialIcons
                name={tab.icon}
                size={20}
                color={activeTab === tab.id ? '#FFFFFF' : '#C7D2FE'}
              />
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab.id && styles.tabTextActive,
                ]}
              >
                {tab.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </LinearGradient>

      {/* Period Selector */}
      {renderPeriodSelector()}

      {/* Tab Content */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={refreshAnalytics}
            colors={['#6366F1']}
            tintColor="#6366F1"
          />
        }
      >
        {renderTabContent()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#C7D2FE',
    opacity: 0.9,
  },
  tabScrollView: {
    marginHorizontal: -20,
  },
  tabContainer: {
    paddingHorizontal: 20,
    gap: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    gap: 6,
  },
  tabActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#C7D2FE',
  },
  tabTextActive: {
    color: '#FFFFFF',
  },
  periodSelector: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: '#F1F5F9',
    marginRight: 8,
  },
  periodButtonActive: {
    backgroundColor: '#6366F1',
  },
  periodText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
  },
  periodTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
  },
});
